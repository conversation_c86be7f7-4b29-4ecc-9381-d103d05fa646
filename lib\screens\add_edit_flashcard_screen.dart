import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../services/flashcard_service.dart';
import '../models/flashcard.dart';
import '../theme/app_theme.dart';
import '../utils/family_id_utils.dart';
import '../widgets/optimized_flashcard_image.dart';

class AddEditFlashcardScreen extends StatefulWidget {
  final Flashcard? flashcard;

  const AddEditFlashcardScreen({super.key, this.flashcard});

  bool get isEditing => flashcard != null;

  @override
  State<AddEditFlashcardScreen> createState() => _AddEditFlashcardScreenState();
}

class _AddEditFlashcardScreenState extends State<AddEditFlashcardScreen> {
  final _formKey = GlobalKey<FormState>();
  final _wordController = TextEditingController();
  final _imagePicker = ImagePicker();

  File? _selectedImage;
  String? _currentImagePath;
  String? _currentFirebaseImageUrl;
  bool _isSaving = false;
  bool _isLoadingImage = false;
  String? _selectedCategoryId;
  String? _familyId;

  @override
  void initState() {
    super.initState();
    _loadFamilyIdAndCategories();
    if (widget.isEditing) {
      _wordController.text = widget.flashcard!.word;
      _currentImagePath = widget.flashcard!.localImagePath;
      _currentFirebaseImageUrl = widget.flashcard!.firebaseImageUrl;
      _selectedCategoryId = null; // will set after categories load
    }
  }

  Future<void> _loadFamilyIdAndCategories() async {
    final id = await FamilyIdUtils.getFamilyId();
    if (!mounted) return;

    setState(() {
      _familyId = id;
    });

    if (!mounted) return;
    final flashcardService = Provider.of<FlashcardService>(
      context,
      listen: false,
    );
    final categories = flashcardService.categories;
    if (categories.isNotEmpty) {
      if (widget.isEditing) {
        final cat = categories.firstWhere(
          (c) => c.name == widget.flashcard!.category,
          orElse: () => categories.first,
        );
        setState(() {
          _selectedCategoryId = cat.id;
        });
      } else if (_selectedCategoryId == null) {
        setState(() {
          _selectedCategoryId = categories.first.id;
        });
      }
    }
  }

  @override
  void dispose() {
    _wordController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      setState(() {
        _isLoadingImage = true;
      });

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final compressedImage = await _compressImage(File(pickedFile.path));
        if (compressedImage != null) {
          setState(() {
            _selectedImage = compressedImage;
            _currentImagePath = compressedImage.path;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingImage = false;
      });
    }
  }

  Future<File?> _compressImage(File imageFile) async {
    try {
      final directory = await getTemporaryDirectory();
      final targetPath = path.join(
        directory.path,
        'compressed_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        targetPath,
        quality: 85,
        minWidth: 300,
        minHeight: 300,
        format: CompressFormat.jpeg,
      );

      return compressedFile != null ? File(compressedFile.path) : null;
    } catch (e) {
      debugPrint('Image compression error: $e');
      return imageFile; // Return original if compression fails
    }
  }

  Future<void> _saveFlashcard() async {
    setState(() => _isSaving = true);
    try {
      final word = _wordController.text.trim();
      if (word.isEmpty || _familyId == null || _selectedCategoryId == null) {
        throw Exception('Word, category, and family ID are required.');
      }
      final flashcardService = Provider.of<FlashcardService>(
        context,
        listen: false,
      );
      final categories = flashcardService.categories;
      final category = categories.firstWhere(
        (c) => c.id == _selectedCategoryId,
      );
      String? localImagePath = _currentImagePath;
      if (_selectedImage != null) {
        // Save image to local path if needed (implement as in your logic)
        localImagePath = _selectedImage!.path;
      }
      if (widget.isEditing) {
        final updated = widget.flashcard!.copyWith(
          word: word,
          localImagePath: localImagePath,
          category: category.name,
          familyId: _familyId!,
        );
        await flashcardService.updateFlashcard(updated);
      } else {
        await flashcardService.addFlashcard(
          word: word,
          localImagePath: localImagePath,
          firebaseImageUrl: null,
          category: category.name,
          familyId: _familyId!,
        );
      }
      if (mounted) Navigator.of(context).pop();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save flashcard: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Widget _buildImagePreview() {
    // If a new image is selected, show it
    if (_selectedImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.file(
          _selectedImage!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: 200,
        ),
      );
    }

    // If no image data at all, show placeholder
    if ((_currentImagePath == null || _currentImagePath!.isEmpty) &&
        (_currentFirebaseImageUrl == null ||
            _currentFirebaseImageUrl!.isEmpty)) {
      return Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.textSecondary.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 60,
              color: AppTheme.textSecondary.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 8),
            Text(
              'No image selected',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondary.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    // Use OptimizedFlashcardImage for existing flashcard images
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: OptimizedFlashcardImage(
        localImagePath: _currentImagePath,
        firebaseImageUrl: _currentFirebaseImageUrl,
        width: double.infinity,
        height: 200,
        fit: BoxFit.cover,
        placeholder: Container(
          height: 200,
          color: AppTheme.backgroundColor,
          child: const Center(
            child: CircularProgressIndicator(color: AppTheme.primaryColor),
          ),
        ),
        errorWidget: Container(
          height: 200,
          color: AppTheme.backgroundColor,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported_outlined,
                size: 40,
                color: AppTheme.textSecondary.withValues(alpha: 0.7),
              ),
              const SizedBox(height: 8),
              Text(
                'Image not available',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final flashcardService = Provider.of<FlashcardService>(context);
    final categories = flashcardService.categories;
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isEditing ? 'Edit Flashcard' : 'Add Flashcard'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveFlashcard,
              child: const Text(
                'Save',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Image section
              _buildImagePreview(),

              const SizedBox(height: 16),

              // Image picker button
              ElevatedButton.icon(
                onPressed: _isLoadingImage ? null : _pickImage,
                icon: _isLoadingImage
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.photo_library),
                label: Text(_isLoadingImage ? 'Loading...' : 'Select Image'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),

              const SizedBox(height: 24),

              // Word input
              TextFormField(
                controller: _wordController,
                decoration: const InputDecoration(
                  labelText: 'Enter Word',
                  hintText: 'e.g., Apple, Cat, House',
                  prefixIcon: Icon(Icons.text_fields),
                ),
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a word';
                  }
                  if (value.trim().length < 2) {
                    return 'Word must be at least 2 characters long';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Category dropdown
              DropdownButtonFormField<String>(
                value: _selectedCategoryId,
                items: categories
                    .map(
                      (cat) => DropdownMenuItem(
                        value: cat.id,
                        child: Text(cat.name),
                      ),
                    )
                    .toList(),
                onChanged: (val) => setState(() => _selectedCategoryId = val),
                decoration: const InputDecoration(labelText: 'Category'),
              ),

              const SizedBox(height: 32),

              // Save button
              ElevatedButton(
                onPressed: _isSaving ? null : _saveFlashcard,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: _isSaving
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('Saving...'),
                        ],
                      )
                    : Text(
                        widget.isEditing
                            ? 'Update Flashcard'
                            : 'Create Flashcard',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
