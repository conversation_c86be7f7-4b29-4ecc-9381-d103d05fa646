import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/flashcard_service.dart';
import '../services/firebase_service.dart';
import '../models/flashcard.dart';
import '../theme/app_theme.dart';
import 'add_edit_flashcard_screen.dart';

class FlashcardListScreen extends StatefulWidget {
  const FlashcardListScreen({super.key});

  @override
  State<FlashcardListScreen> createState() => _FlashcardListScreenState();
}

class _FlashcardListScreenState extends State<FlashcardListScreen> {
  bool _isInitialized = false;
  bool _isSyncing = false;
  final Map<String, bool> _categoryEnabled = {};
  final Map<String, bool> _categoryExpanded = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final flashcardService = context.read<FlashcardService>();
      await flashcardService.initialize();
      _refreshCategoryStates();
    });
  }

  void _refreshCategoryStates() {
    final flashcardService = context.read<FlashcardService>();
    final categories = flashcardService.categories;
    setState(() {
      _categoryEnabled.clear(); // Clear existing state
      _categoryExpanded.clear(); // Clear existing expanded state
      for (final cat in categories) {
        _categoryEnabled[cat.id] = cat.enabled;
        _categoryExpanded[cat.id] = true; // Default to expanded
      }
      _isInitialized = true;
    });
  }

  Future<void> _downloadFromFirebase() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      final firebaseService = context.read<FirebaseService>();
      final flashcardService = context.read<FlashcardService>();

      // Get all flashcards from Firebase
      final firebaseFlashcards = await firebaseService.getAllFlashcards();

      if (firebaseFlashcards.isNotEmpty) {
        // Clear local storage and insert Firebase data
        await flashcardService.clearAllFlashcards();
        await flashcardService.bulkInsertFlashcards(firebaseFlashcards);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Downloaded ${firebaseFlashcards.length} flashcards',
              ),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No flashcards found on server'),
              backgroundColor: AppTheme.textSecondary,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      // Refresh category states after sync
      _refreshCategoryStates();
      setState(() {
        _isSyncing = false;
      });
    }
  }

  Future<void> _deleteFlashcard(Flashcard flashcard) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Flashcard'),
        content: Text('Are you sure you want to delete "${flashcard.word}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final flashcardService = context.read<FlashcardService>();
      final firebaseService = context.read<FirebaseService>();

      try {
        // Delete from Firebase first
        if (flashcard.isSynced && flashcard.firebaseImageUrl != null) {
          // Remove or replace any call to firebaseService.deleteImage. If image deletion is needed, implement it in FirebaseService or remove the call.
          // For now, we'll just delete the flashcard from Firebase.
          await firebaseService.deleteFlashcard(flashcard.id);
        }

        // Delete from local storage
        await flashcardService.deleteFlashcard(flashcard.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Deleted "${flashcard.word}"'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Delete failed: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }

  void _editFlashcard(Flashcard flashcard) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditFlashcardScreen(flashcard: flashcard),
      ),
    );
  }

  void _addFlashcard() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddEditFlashcardScreen()),
    );
  }

  void _toggleCategory(String categoryId, bool? value) {
    setState(() {
      _categoryEnabled[categoryId] = value ?? false;
    });
    final flashcardService = context.read<FlashcardService>();
    flashcardService.setCategoryEnabled(categoryId, value ?? false);
  }

  Widget _buildCategorySection(
    String categoryName,
    String categoryId,
    List<Flashcard> cards,
  ) {
    final isExpanded = _categoryExpanded[categoryId] ?? true;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category header with checkbox, name, and expand/collapse button
          InkWell(
            onTap: () => _toggleCategoryExpansion(categoryId),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Checkbox(
                    value: _categoryEnabled[categoryId] ?? true,
                    onChanged: (val) => _toggleCategory(categoryId, val),
                  ),
                  Expanded(
                    child: Text(
                      '$categoryName (${cards.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppTheme.primaryColor,
                    ),
                    onPressed: () => _toggleCategoryExpansion(categoryId),
                    tooltip: isExpanded ? 'Collapse' : 'Expand',
                  ),
                ],
              ),
            ),
          ),
          // Collapsible content
          if (isExpanded && cards.isNotEmpty) ...[
            const Divider(height: 1),
            // Header row
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                border: Border(
                  bottom: BorderSide(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  // # column - smallest width (40px)
                  SizedBox(
                    width: 40,
                    child: Text(
                      '#',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Word column - largest width (expanded)
                  Expanded(
                    child: Text(
                      'Word',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                  // Actions column - smaller width, right aligned (100px)
                  SizedBox(
                    width: 100,
                    child: Text(
                      'Actions',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Data rows
            SizedBox(
              height: cards.length * 56.0, // Approximate height per row
              child: ListView.builder(
                itemCount: cards.length,
                itemBuilder: (context, i) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.2),
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        // # column - smallest width (40px), left aligned
                        SizedBox(
                          width: 40,
                          child: Text(
                            '${i + 1}',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Word column - largest width (expanded), left aligned
                        Expanded(
                          child: Text(
                            cards[i].word,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        // Actions column - smaller width (100px), right aligned
                        SizedBox(
                          width: 100,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(
                                  Icons.edit,
                                  color: AppTheme.primaryColor,
                                  size: 20,
                                ),
                                onPressed: () => _editFlashcard(cards[i]),
                                tooltip: 'Edit',
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.delete,
                                  color: AppTheme.errorColor,
                                  size: 20,
                                ),
                                onPressed: () => _deleteFlashcard(cards[i]),
                                tooltip: 'Delete',
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ] else if (isExpanded && cards.isEmpty) ...[
            const Divider(height: 1),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'No flashcards in this category',
                style: TextStyle(
                  color: AppTheme.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _toggleCategoryExpansion(String categoryId) {
    setState(() {
      _categoryExpanded[categoryId] = !(_categoryExpanded[categoryId] ?? true);
    });
  }

  bool _areAllCategoriesExpanded() {
    if (_categoryExpanded.isEmpty) return true;
    return _categoryExpanded.values.every((expanded) => expanded);
  }

  void _toggleAllCategories() {
    final shouldExpand = !_areAllCategoriesExpanded();
    setState(() {
      for (final categoryId in _categoryExpanded.keys) {
        _categoryExpanded[categoryId] = shouldExpand;
      }
    });
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.list_outlined, size: 80, color: AppTheme.textSecondary),
          SizedBox(height: 16),
          Text(
            'No flashcards yet',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Tap the + button to add your first flashcard',
            style: TextStyle(fontSize: 16, color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Word List'),
          backgroundColor: AppTheme.primaryColor,
        ),
        body: const Center(
          child: CircularProgressIndicator(color: AppTheme.primaryColor),
        ),
      );
    }

    return Consumer<FlashcardService>(
      builder: (context, flashcardService, child) {
        final categories = flashcardService.categories
            .where((c) => c.name.trim().toLowerCase() != 'all')
            .toList();
        final flashcards = flashcardService.flashcards;
        final flashcardsByCategory = <String, List<Flashcard>>{};

        // Build flashcards by category for ALL categories (not just enabled ones)
        for (final cat in categories) {
          flashcardsByCategory[cat.id] = flashcards
              .where((f) => f.category == cat.name)
              .toList();
        }

        // Check if there are any flashcards at all
        final hasAnyFlashcards = categories.any(
          (cat) => (flashcardsByCategory[cat.id]?.isNotEmpty ?? false),
        );
        final isEmpty = categories.isEmpty || !hasAnyFlashcards;
        return Scaffold(
          appBar: AppBar(
            title: Text('Word List (${flashcardService.count})'),
            backgroundColor: AppTheme.primaryColor,
            actions: [
              IconButton(
                onPressed: _toggleAllCategories,
                icon: Icon(
                  _areAllCategoriesExpanded()
                      ? Icons.unfold_less
                      : Icons.unfold_more,
                ),
                tooltip: _areAllCategoriesExpanded()
                    ? 'Collapse All'
                    : 'Expand All',
              ),
              IconButton(
                onPressed: _isSyncing ? null : _downloadFromFirebase,
                icon: _isSyncing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.download),
                tooltip: 'Download from server',
              ),
              IconButton(
                onPressed: _addFlashcard,
                icon: const Icon(Icons.add),
                tooltip: 'Add flashcard',
              ),
            ],
          ),
          body: flashcardService.isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryColor,
                  ),
                )
              : isEmpty
              ? _buildEmptyState()
              : ListView(
                  padding: const EdgeInsets.all(8),
                  children: [
                    for (final cat in categories)
                      _buildCategorySection(
                        cat.name,
                        cat.id,
                        flashcardsByCategory[cat.id] ?? [],
                      ),
                  ],
                ),
        );
      },
    );
  }
}
