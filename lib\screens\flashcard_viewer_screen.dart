import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/flashcard_service.dart';
import '../services/audio_service.dart';
import '../models/flashcard.dart';
import '../theme/app_theme.dart';
import '../widgets/enhanced_image_viewer.dart';
import 'main_screen.dart';

class FlashcardViewerScreen extends StatefulWidget {
  const FlashcardViewerScreen({super.key});

  @override
  State<FlashcardViewerScreen> createState() => _FlashcardViewerScreenState();
}

class _FlashcardViewerScreenState extends State<FlashcardViewerScreen> {
  late PageController _pageController;
  static const int _kMiddlePage = 500000;
  int _currentIndex = 0;
  int _lastFlashcardCount = 0;
  bool _isInitialized = false;
  double _imageRotation = 0.0; // Rotation angle in radians

  @override
  void initState() {
    super.initState();
    // In initState:
    _pageController = PageController(initialPage: _kMiddlePage);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final flashcardService = context.read<FlashcardService>();
      final audioService = context.read<AudioService>();
      await flashcardService.initialize();
      await audioService.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index, int totalFlashcards) {
    if (!mounted) return;

    final actualIndex = index % totalFlashcards;
    setState(() {
      _currentIndex = actualIndex;
      _imageRotation = 0.0; // Reset rotation when changing flashcards
    });
  }

  void _playAudio(String word) {
    if (!mounted) return;

    try {
      final audioService = context.read<AudioService>();
      audioService.speak(word);
    } catch (e) {
      debugPrint('Error playing audio: $e');
    }
  }

  void _rotateImage() {
    setState(() {
      _imageRotation += 1.5708; // 90 degrees in radians
      if (_imageRotation >= 6.2832) {
        // 360 degrees
        _imageRotation = 0.0;
      }
    });
  }

  Widget _buildEmptyState() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flashcards'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.style_outlined,
                size: 80,
                color: AppTheme.textSecondary,
              ),
              const SizedBox(height: 16),
              const Text(
                'No flashcards to display',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This could be because:',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                '• No categories are selected in Word List\n'
                '• No flashcards have been created yet\n'
                '• All categories are unchecked',
                style: TextStyle(fontSize: 14, color: AppTheme.textSecondary),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  // Navigate to MainScreen and select the Word List tab (usually index 1)
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (_) => MainScreen(initialTabIndex: 1),
                    ),
                  );
                },
                icon: const Icon(Icons.list),
                label: const Text('Go to Word List'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flashcards'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryColor),
      ),
    );
  }

  Widget _buildFlashcardImage(Flashcard flashcard) {
    return Center(
      child: AspectRatio(
        aspectRatio: 4 / 3, // You can adjust this ratio as needed
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Container(
            color: Colors.white,
            child: Transform.rotate(
              angle: _imageRotation,
              child: EnhancedImageViewer(
                localImagePath: flashcard.localImagePath,
                firebaseImageUrl: flashcard.firebaseImageUrl,
                rotation: _imageRotation,
                onRotate: _rotateImage,
                placeholder: Container(
                  color: AppTheme.backgroundColor,
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                errorWidget: Container(
                  color: AppTheme.backgroundColor,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image_outlined,
                          size: 80,
                          color: AppTheme.textSecondary,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No image available',
                          style: TextStyle(
                            fontSize: 18,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFlashcard(Flashcard flashcard) {
    return Stack(
      children: [
        // Background image
        _buildFlashcardImage(flashcard),

        // Gradient overlay for better text readability
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 200,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black54, Colors.black87],
              ),
            ),
          ),
        ),

        // Word label and audio button
        Positioned(
          bottom: 60,
          left: 0,
          right: 0,
          child: Column(
            children: [
              // Word text
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                child: Text(
                  flashcard.word,
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(2, 2),
                        blurRadius: 4,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // Audio button
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => _playAudio(flashcard.word),
                  icon: const Icon(
                    Icons.volume_up,
                    color: Colors.white,
                    size: 32,
                  ),
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildLoadingState();
    }

    return Consumer<FlashcardService>(
      builder: (context, flashcardService, child) {
        if (flashcardService.isLoading) {
          return _buildLoadingState();
        }

        // Get flashcards from enabled categories only
        final flashcards = flashcardService.getEnabledFlashcards();

        // Reset current index if flashcard list changed
        if (flashcards.length != _lastFlashcardCount) {
          _lastFlashcardCount = flashcards.length;

          // Reset index when flashcard list changes
          if (flashcards.isNotEmpty) {
            // If current index is out of bounds, reset to 0
            if (_currentIndex >= flashcards.length) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {
                    _currentIndex = 0;
                  });
                  // Reset to middle of range for circular navigation
                  _pageController.animateToPage(
                    500000, // Middle of the large range
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              });
            }
          } else {
            // If no flashcards, reset index to 0
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _currentIndex = 0;
                });
              }
            });
          }
        }

        if (flashcards.isEmpty) {
          return _buildEmptyState();
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Flashcards (${_currentIndex + 1}/${flashcards.length})',
            ),
            backgroundColor: AppTheme.primaryColor,
          ),
          body: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              _onPageChanged(index, flashcards.length);

              // Reset to middle if near the edges to maintain circular navigation
              if (index <= 100 || index >= flashcards.length * 1000 - 100) {
                final newIndex =
                    _kMiddlePage + (_currentIndex % flashcards.length);
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted && _pageController.hasClients) {
                    _pageController.jumpToPage(newIndex);
                  }
                });
              }
            },
            itemCount:
                flashcards.length * 1000, // Large number for circular effect
            itemBuilder: (context, index) {
              final actualIndex = index % flashcards.length;
              return _buildFlashcard(flashcards[actualIndex]);
            },
          ),
        );
      },
    );
  }
}
