import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  FlutterTts? _flutterTts;
  bool _isInitialized = false;
  bool _isSpeaking = false;
  bool _isProcessing = false;
  String _currentLanguage = 'en-US';
  double _speechRate = 0.5;
  double _volume = 1.0;
  double _pitch = 1.0;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isSpeaking => _isSpeaking;
  String get currentLanguage => _currentLanguage;
  double get speechRate => _speechRate;
  double get volume => _volume;
  double get pitch => _pitch;

  // Initialize TTS
  Future<void> initialize() async {
    try {
      _flutterTts = FlutterTts();

      if (_flutterTts != null) {
        // Set up event handlers
        _flutterTts!.setStartHandler(() {
          _isSpeaking = true;
          debugPrint('TTS: Started speaking');
        });

        _flutterTts!.setCompletionHandler(() {
          _isSpeaking = false;
          debugPrint('TTS: Completed speaking');
        });

        _flutterTts!.setErrorHandler((msg) {
          _isSpeaking = false;
          debugPrint('TTS Error: $msg');
        });

        _flutterTts!.setCancelHandler(() {
          _isSpeaking = false;
          debugPrint('TTS: Cancelled');
        });

        _flutterTts!.setPauseHandler(() {
          _isSpeaking = false;
          debugPrint('TTS: Paused');
        });

        _flutterTts!.setContinueHandler(() {
          _isSpeaking = true;
          debugPrint('TTS: Continued');
        });

        // Configure TTS settings
        await _configureTts();

        _isInitialized = true;
        debugPrint('AudioService: Initialized successfully');
      }
    } catch (e) {
      debugPrint('AudioService initialization error: $e');
      _isInitialized = false;
    }
  }

  // Configure TTS settings
  Future<void> _configureTts() async {
    if (_flutterTts == null) return;

    try {
      // Set language
      await _flutterTts!.setLanguage(_currentLanguage);

      // Set speech rate (0.0 to 1.0)
      await _flutterTts!.setSpeechRate(_speechRate);

      // Set volume (0.0 to 1.0)
      await _flutterTts!.setVolume(_volume);

      // Set pitch (0.5 to 2.0)
      await _flutterTts!.setPitch(_pitch);

      // Platform-specific configurations
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        await _flutterTts!.setSharedInstance(true);
        await _flutterTts!.setIosAudioCategory(
          IosTextToSpeechAudioCategory.playback,
          [
            IosTextToSpeechAudioCategoryOptions.allowBluetooth,
            IosTextToSpeechAudioCategoryOptions.allowBluetoothA2DP,
            IosTextToSpeechAudioCategoryOptions.mixWithOthers,
          ],
          IosTextToSpeechAudioMode.spokenAudio,
        );
      }
    } catch (e) {
      debugPrint('TTS configuration error: $e');
    }
  }

  // Speak a word or text
  Future<void> speak(String text) async {
    // Prevent multiple simultaneous speak calls
    if (_isProcessing) {
      debugPrint('AudioService: Already processing, ignoring speak request');
      return;
    }

    _isProcessing = true;

    try {
      if (!_isInitialized || _flutterTts == null) {
        debugPrint(
          'AudioService: Not initialized, attempting to initialize...',
        );
        await initialize();
        if (!_isInitialized) {
          debugPrint('AudioService: Failed to initialize');
          return;
        }
      }

      // Stop any current speech
      if (_isSpeaking) {
        await stop();
        // Wait a bit for the stop to complete
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Clean the text
      final cleanText = text.trim();
      if (cleanText.isEmpty) {
        debugPrint('AudioService: Empty text provided');
        return;
      }

      debugPrint('AudioService: Speaking "$cleanText"');
      await _flutterTts!.speak(cleanText);
    } catch (e) {
      debugPrint('AudioService speak error: $e');
      _isSpeaking = false;
    } finally {
      _isProcessing = false;
    }
  }

  // Stop speaking
  Future<void> stop() async {
    if (_flutterTts == null) return;

    try {
      await _flutterTts!.stop();
      _isSpeaking = false;
    } catch (e) {
      debugPrint('AudioService stop error: $e');
    }
  }

  // Pause speaking
  Future<void> pause() async {
    if (_flutterTts == null) return;

    try {
      await _flutterTts!.pause();
    } catch (e) {
      debugPrint('AudioService pause error: $e');
    }
  }

  // Set language
  Future<void> setLanguage(String language) async {
    if (_flutterTts == null) return;

    try {
      _currentLanguage = language;
      await _flutterTts!.setLanguage(language);
      debugPrint('AudioService: Language set to $language');
    } catch (e) {
      debugPrint('AudioService setLanguage error: $e');
    }
  }

  // Set speech rate
  Future<void> setSpeechRate(double rate) async {
    if (_flutterTts == null) return;

    try {
      _speechRate = rate.clamp(0.0, 1.0);
      await _flutterTts!.setSpeechRate(_speechRate);
      debugPrint('AudioService: Speech rate set to $_speechRate');
    } catch (e) {
      debugPrint('AudioService setSpeechRate error: $e');
    }
  }

  // Set volume
  Future<void> setVolume(double volume) async {
    if (_flutterTts == null) return;

    try {
      _volume = volume.clamp(0.0, 1.0);
      await _flutterTts!.setVolume(_volume);
      debugPrint('AudioService: Volume set to $_volume');
    } catch (e) {
      debugPrint('AudioService setVolume error: $e');
    }
  }

  // Set pitch
  Future<void> setPitch(double pitch) async {
    if (_flutterTts == null) return;

    try {
      _pitch = pitch.clamp(0.5, 2.0);
      await _flutterTts!.setPitch(_pitch);
      debugPrint('AudioService: Pitch set to $_pitch');
    } catch (e) {
      debugPrint('AudioService setPitch error: $e');
    }
  }

  // Get available languages
  Future<List<String>> getAvailableLanguages() async {
    if (_flutterTts == null) return [];

    try {
      final languages = await _flutterTts!.getLanguages;
      return List<String>.from(languages ?? []);
    } catch (e) {
      debugPrint('AudioService getAvailableLanguages error: $e');
      return [];
    }
  }

  // Get available voices
  Future<List<Map<String, String>>> getAvailableVoices() async {
    if (_flutterTts == null) return [];

    try {
      final voices = await _flutterTts!.getVoices;
      return List<Map<String, String>>.from(voices ?? []);
    } catch (e) {
      debugPrint('AudioService getAvailableVoices error: $e');
      return [];
    }
  }

  // Check if TTS is available
  Future<bool> isLanguageAvailable(String language) async {
    if (_flutterTts == null) return false;

    try {
      final result = await _flutterTts!.isLanguageAvailable(language);
      return result == 1;
    } catch (e) {
      debugPrint('AudioService isLanguageAvailable error: $e');
      return false;
    }
  }

  // Dispose resources
  void dispose() {
    _flutterTts?.stop();
    _flutterTts = null;
    _isInitialized = false;
    _isSpeaking = false;
  }
}
