import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

// Background function for base64 decoding
Uint8List _decodeBase64(String base64String) {
  return base64Decode(base64String);
}

class OptimizedFlashcardImage extends StatefulWidget {
  final String? localImagePath;
  final String? firebaseImageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedFlashcardImage({
    super.key,
    this.localImagePath,
    this.firebaseImageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<OptimizedFlashcardImage> createState() =>
      _OptimizedFlashcardImageState();
}

class _OptimizedFlashcardImageState extends State<OptimizedFlashcardImage> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(OptimizedFlashcardImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.localImagePath != widget.localImagePath ||
        oldWidget.firebaseImageUrl != widget.firebaseImageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _imageBytes = null;
    });

    try {
      Uint8List? bytes;

      // Try local image first
      if (widget.localImagePath != null && widget.localImagePath!.isNotEmpty) {
        final file = File(widget.localImagePath!);
        if (await file.exists()) {
          bytes = await file.readAsBytes();
        }
      }

      // Fallback to Firebase base64 image (decode in background)
      if (bytes == null &&
          widget.firebaseImageUrl != null &&
          widget.firebaseImageUrl!.isNotEmpty) {
        try {
          bytes = await compute(_decodeBase64, widget.firebaseImageUrl!);
        } catch (e) {
          debugPrint('Error decoding base64 image: $e');
        }
      }

      if (mounted) {
        setState(() {
          _imageBytes = bytes;
          _isLoading = false;
          _hasError = bytes == null;
        });
      }
    } catch (e) {
      debugPrint('Error loading image: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ??
          Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
    }

    if (_hasError || _imageBytes == null) {
      return widget.errorWidget ??
          Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.image_not_supported,
              color: Colors.grey,
              size: 48,
            ),
          );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.memory(
        _imageBytes!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        gaplessPlayback: true,
        errorBuilder: (context, error, stackTrace) {
          return widget.errorWidget ??
              Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 48,
                ),
              );
        },
      ),
    );
  }
}

// Extension to get display image path with fallback
extension FlashcardImageExtension on dynamic {
  String? get displayImagePath {
    if (this.localImagePath != null && this.localImagePath!.isNotEmpty) {
      return this.localImagePath;
    }
    return this.firebaseImageUrl;
  }

  bool get hasImage {
    return displayImagePath != null && displayImagePath!.isNotEmpty;
  }
}
