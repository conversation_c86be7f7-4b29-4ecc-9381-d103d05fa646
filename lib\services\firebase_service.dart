import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/flashcard.dart';
import '../models/category.dart' as model;

class FirebaseService {
  static const String _collectionName = 'flashcards';
  static const String _categoryCollection = 'categories';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Save flashcard to Firestore (image as base64 string)
  Future<bool> saveFlashcard(Flashcard flashcard, {File? imageFile}) async {
    try {
      String? imageBase64 = flashcard.firebaseImageUrl;

      // If we have a local image path but no base64 data, convert it
      if (imageBase64 == null && flashcard.localImagePath != null) {
        try {
          final file = File(flashcard.localImagePath!);
          if (await file.exists()) {
            final bytes = await file.readAsBytes();
            imageBase64 = base64Encode(bytes);
          }
        } catch (e) {
          debugPrint('Error reading local image file: $e');
        }
      }

      // If imageFile is provided, use it (for new uploads)
      if (imageFile != null) {
        final bytes = await imageFile.readAsBytes();
        imageBase64 = base64Encode(bytes);
      }

      final data = flashcard
          .copyWith(firebaseImageUrl: imageBase64)
          .toFirebaseMap();
      await _firestore.collection(_collectionName).doc(flashcard.id).set(data);
      return true;
    } catch (e) {
      debugPrint('Save flashcard error: $e');
      return false;
    }
  }

  // Update flashcard in Firestore
  Future<bool> updateFlashcard(Flashcard flashcard) async {
    try {
      String? imageBase64 = flashcard.firebaseImageUrl;

      // If we have a local image path but no base64 data, convert it
      if (imageBase64 == null && flashcard.localImagePath != null) {
        try {
          final file = File(flashcard.localImagePath!);
          if (await file.exists()) {
            final bytes = await file.readAsBytes();
            imageBase64 = base64Encode(bytes);
          }
        } catch (e) {
          debugPrint('Error reading local image file: $e');
        }
      }

      final data = flashcard
          .copyWith(firebaseImageUrl: imageBase64)
          .toFirebaseMap();

      await _firestore
          .collection(_collectionName)
          .doc(flashcard.id)
          .update(data);

      return true;
    } catch (e) {
      debugPrint('Update flashcard error: $e');
      return false;
    }
  }

  // Delete flashcard from Firestore
  Future<bool> deleteFlashcard(String flashcardId) async {
    try {
      await _firestore.collection(_collectionName).doc(flashcardId).delete();

      return true;
    } catch (e) {
      debugPrint('Delete flashcard error: $e');
      return false;
    }
  }

  // Get all flashcards from Firestore
  Future<List<Flashcard>> getAllFlashcards() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(_collectionName)
          .orderBy('updatedAt', descending: true)
          .get();

      return snapshot.docs
          .map(
            (doc) =>
                Flashcard.fromFirebaseMap(doc.data() as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      debugPrint('Get all flashcards error: $e');
      return [];
    }
  }

  // Get flashcard by ID from Firestore
  Future<Flashcard?> getFlashcardById(String id) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(_collectionName)
          .doc(id)
          .get();

      if (doc.exists) {
        return Flashcard.fromFirebaseMap(doc.data() as Map<String, dynamic>);
      }

      return null;
    } catch (e) {
      debugPrint('Get flashcard by ID error: $e');
      return null;
    }
  }

  // Listen to flashcards changes (real-time updates)
  Stream<List<Flashcard>> watchFlashcards() {
    return _firestore
        .collection(_collectionName)
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => Flashcard.fromFirebaseMap(doc.data()))
              .toList(),
        );
  }

  // Batch operations for sync
  Future<bool> batchSaveFlashcards(List<Flashcard> flashcards) async {
    try {
      final WriteBatch batch = _firestore.batch();

      for (final flashcard in flashcards) {
        final DocumentReference ref = _firestore
            .collection(_collectionName)
            .doc(flashcard.id);
        batch.set(ref, flashcard.toFirebaseMap());
      }

      await batch.commit();
      return true;
    } catch (e) {
      debugPrint('Batch save flashcards error: $e');
      return false;
    }
  }

  // Check if flashcard exists in Firestore
  Future<bool> flashcardExists(String id) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(_collectionName)
          .doc(id)
          .get();

      return doc.exists;
    } catch (e) {
      debugPrint('Check flashcard exists error: $e');
      return false;
    }
  }

  // Get flashcards updated after a specific timestamp
  Future<List<Flashcard>> getFlashcardsUpdatedAfter(DateTime timestamp) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(_collectionName)
          .where('updatedAt', isGreaterThan: timestamp.millisecondsSinceEpoch)
          .orderBy('updatedAt', descending: true)
          .get();

      return snapshot.docs
          .map(
            (doc) =>
                Flashcard.fromFirebaseMap(doc.data() as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      debugPrint('Get flashcards updated after error: $e');
      return [];
    }
  }

  // Sync operations helper
  Future<Map<String, dynamic>> getSyncInfo() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(_collectionName)
          .orderBy('updatedAt', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        final latestDoc = snapshot.docs.first;
        final data = latestDoc.data() as Map<String, dynamic>;
        return {
          'lastUpdated': DateTime.fromMillisecondsSinceEpoch(
            data['updatedAt'] ?? 0,
          ),
          'totalCount': await _getTotalFlashcardCount(),
        };
      }

      return {
        'lastUpdated': DateTime.fromMillisecondsSinceEpoch(0),
        'totalCount': 0,
      };
    } catch (e) {
      debugPrint('Get sync info error: $e');
      return {
        'lastUpdated': DateTime.fromMillisecondsSinceEpoch(0),
        'totalCount': 0,
      };
    }
  }

  Future<int> _getTotalFlashcardCount() async {
    try {
      final AggregateQuerySnapshot snapshot = await _firestore
          .collection(_collectionName)
          .count()
          .get();

      return snapshot.count ?? 0;
    } catch (e) {
      debugPrint('Get total flashcard count error: $e');
      return 0;
    }
  }

  // Save category to Firestore
  Future<bool> saveCategory(model.Category category) async {
    try {
      final data = category.toFirebaseMap();
      await _firestore
          .collection(_categoryCollection)
          .doc(category.id)
          .set(data);
      return true;
    } catch (e) {
      debugPrint('Save category error: $e');
      return false;
    }
  }

  // Update category in Firestore
  Future<bool> updateCategory(model.Category category) async {
    try {
      await _firestore
          .collection(_categoryCollection)
          .doc(category.id)
          .update(category.toFirebaseMap());
      return true;
    } catch (e) {
      debugPrint('Update category error: $e');
      return false;
    }
  }

  // Delete category from Firestore
  Future<bool> deleteCategory(String categoryId) async {
    try {
      await _firestore.collection(_categoryCollection).doc(categoryId).delete();
      return true;
    } catch (e) {
      debugPrint('Delete category error: $e');
      return false;
    }
  }

  // Get all categories from Firestore
  Future<List<model.Category>> getAllCategories() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(_categoryCollection)
          .orderBy('updatedAt', descending: true)
          .get();
      return snapshot.docs
          .map(
            (doc) => model.Category.fromFirebaseMap(
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Get all categories error: $e');
      return [];
    }
  }

  // Get categories by familyId
  Future<List<model.Category>> getCategoriesByFamilyId(String familyId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(_categoryCollection)
          .where('familyId', isEqualTo: familyId)
          .orderBy('updatedAt', descending: true)
          .get();
      return snapshot.docs
          .map(
            (doc) => model.Category.fromFirebaseMap(
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Get categories by familyId error: $e');
      return [];
    }
  }
}
