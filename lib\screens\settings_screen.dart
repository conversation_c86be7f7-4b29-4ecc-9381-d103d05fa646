import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import '../services/flashcard_service.dart';
import '../services/firebase_service.dart';
import '../services/sync_service.dart';
import '../models/category.dart' as model;
import '../utils/family_id_utils.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String? _familyId;
  bool _editFamilyId = false;
  final TextEditingController _familyIdController = TextEditingController();
  final TextEditingController _addCategoryController = TextEditingController();
  String? _editingCategoryId;
  final TextEditingController _editCategoryController = TextEditingController();
  bool _isSyncing = false;
  bool _isClearing = false;
  bool _isCategorySyncing = false;
  final Set<String> _pendingSyncCategories = {};

  @override
  void initState() {
    super.initState();
    _loadFamilyId();
  }

  Future<void> _loadFamilyId() async {
    final id = await FamilyIdUtils.getFamilyId();
    setState(() {
      _familyId = id;
      _familyIdController.text = id;
    });
  }

  Future<void> _saveFamilyId() async {
    final newId = _familyIdController.text.trim();
    if (newId.isNotEmpty) {
      await FamilyIdUtils.setFamilyId(newId);
      setState(() {
        _familyId = newId;
        _editFamilyId = false;
      });
    }
  }

  Future<void> _clearLocalData(BuildContext context) async {
    setState(() => _isClearing = true);

    final scaffoldMessenger = ScaffoldMessenger.of(context);
    await Provider.of<FlashcardService>(
      context,
      listen: false,
    ).clearAllFlashcards();
    // Optionally clear categories as well
    // await Provider.of<FlashcardService>(context, listen: false).clearAllCategories();

    setState(() => _isClearing = false);
    if (mounted) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text('Local data cleared.')),
      );
    }
  }

  Future<void> _syncNow(BuildContext context) async {
    setState(() => _isSyncing = true);

    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final syncService = Provider.of<SyncService>(context, listen: false);
    final result = await syncService.fullSync();

    setState(() => _isSyncing = false);
    if (mounted) {
      scaffoldMessenger.showSnackBar(SnackBar(content: Text(result.message)));
    }
  }

  Future<void> _syncCategoryToFirebase(model.Category category) async {
    try {
      final firebaseService = Provider.of<FirebaseService>(
        context,
        listen: false,
      );
      final success = await firebaseService.saveCategory(category);

      if (mounted) {
        setState(() {
          if (success) {
            _pendingSyncCategories.remove(category.id);
          }
        });
      }
    } catch (e) {
      debugPrint('Failed to sync category to Firebase: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final flashcardService = Provider.of<FlashcardService>(context);
    final categories = flashcardService.categories
        .where((c) => c.name.trim().isNotEmpty && c.name != 'All')
        .toList();
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Family ID Section
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Label centered
              Text(
                'Family Id',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              // Value smaller, centered, on its own line
              _editFamilyId
                  ? TextField(
                      controller: _familyIdController,
                      decoration: const InputDecoration(labelText: 'Family ID'),
                      textAlign: TextAlign.center,
                    )
                  : SelectableText(
                      _familyId ?? '',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyLarge?.copyWith(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
              const SizedBox(height: 8),
              // Icons row: copy and edit (no copy in edit mode)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (!_editFamilyId)
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: _familyId == null || _familyId!.isEmpty
                          ? null
                          : () {
                              Clipboard.setData(
                                ClipboardData(text: _familyId!),
                              );
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Family ID copied!'),
                                ),
                              );
                            },
                    ),
                  IconButton(
                    icon: Icon(_editFamilyId ? Icons.save : Icons.edit),
                    onPressed: () async {
                      if (_editFamilyId) {
                        await _saveFamilyId();
                      } else {
                        setState(() => _editFamilyId = true);
                      }
                    },
                  ),
                  if (_editFamilyId)
                    IconButton(
                      icon: const Icon(Icons.cancel),
                      onPressed: () {
                        setState(() {
                          _editFamilyId = false;
                          _familyIdController.text = _familyId ?? '';
                        });
                      },
                    ),
                ],
              ),
            ],
          ),
          const Divider(height: 32),
          // Category Management
          Text('Categories', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _addCategoryController,
                  decoration: const InputDecoration(labelText: 'Add Category'),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _isCategorySyncing
                    ? null
                    : () async {
                        final name = _addCategoryController.text.trim();
                        if (name.isNotEmpty && _familyId != null) {
                          setState(() => _isCategorySyncing = true);

                          final category = await flashcardService.addCategory(
                            name: name,
                            familyId: _familyId!,
                          );

                          if (category != null) {
                            _pendingSyncCategories.add(category.id);
                            _addCategoryController.clear();

                            // Try to sync to Firebase
                            _syncCategoryToFirebase(category);
                          }

                          setState(() => _isCategorySyncing = false);
                        }
                      },
              ),
            ],
          ),
          ...categories.map(
            (cat) => _editingCategoryId == cat.id
                ? ListTile(
                    title: TextField(
                      controller: _editCategoryController,
                      decoration: const InputDecoration(
                        labelText: 'Edit Category',
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.save),
                          onPressed: () async {
                            final name = _editCategoryController.text.trim();
                            if (name.isNotEmpty) {
                              await flashcardService.updateCategory(
                                cat.copyWith(name: name),
                              );
                              setState(() => _editingCategoryId = null);
                            }
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.cancel),
                          onPressed: () =>
                              setState(() => _editingCategoryId = null),
                        ),
                      ],
                    ),
                  )
                : ListTile(
                    title: Row(
                      children: [
                        Expanded(child: Text(cat.name)),
                        if (_pendingSyncCategories.contains(cat.id))
                          const Icon(
                            Icons.sync_problem,
                            color: Colors.orange,
                            size: 16,
                          ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {
                            setState(() {
                              _editingCategoryId = cat.id;
                              _editCategoryController.text = cat.name;
                            });
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () async {
                            final confirm = await showDialog<bool>(
                              context: context,
                              builder: (ctx) => AlertDialog(
                                title: const Text('Delete Category'),
                                content: Text('Delete category "${cat.name}"?'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(ctx, false),
                                    child: const Text('Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.pop(ctx, true),
                                    child: const Text('Delete'),
                                  ),
                                ],
                              ),
                            );
                            if (confirm == true) {
                              await flashcardService.deleteCategory(cat.id);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
          ),
          const Divider(height: 32),
          // Sync and Clear Data
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: _isSyncing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.sync),
                  label: const Text('Sync Now'),
                  onPressed: _isSyncing ? null : () => _syncNow(context),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  icon: _isClearing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.delete_forever),
                  label: const Text('Clear Data'),
                  onPressed: _isClearing
                      ? null
                      : () => _clearLocalData(context),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
