import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';

enum ImageDisplayMode { cover, contain, fit, fill }

class EnhancedImageViewer extends StatefulWidget {
  final String? localImagePath;
  final String? firebaseImageUrl;
  final double rotation;
  final VoidCallback? onRotate;
  final Widget? placeholder;
  final Widget? errorWidget;

  const EnhancedImageViewer({
    super.key,
    this.localImagePath,
    this.firebaseImageUrl,
    this.rotation = 0.0,
    this.onRotate,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<EnhancedImageViewer> createState() => _EnhancedImageViewerState();
}

class _EnhancedImageViewerState extends State<EnhancedImageViewer>
    with TickerProviderStateMixin {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  bool _hasError = false;
  ImageDisplayMode _displayMode = ImageDisplayMode.cover;

  late TransformationController _transformationController;
  late AnimationController _animationController;
  Animation<Matrix4>? _animation;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _loadImage();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(EnhancedImageViewer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.localImagePath != widget.localImagePath ||
        oldWidget.firebaseImageUrl != widget.firebaseImageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _imageBytes = null;
    });

    try {
      Uint8List? bytes;

      // Try local image first
      if (widget.localImagePath != null && widget.localImagePath!.isNotEmpty) {
        final file = File(widget.localImagePath!);
        if (await file.exists()) {
          bytes = await file.readAsBytes();
        }
      }

      // Fallback to Firebase base64 image
      if (bytes == null &&
          widget.firebaseImageUrl != null &&
          widget.firebaseImageUrl!.isNotEmpty) {
        try {
          bytes = base64Decode(widget.firebaseImageUrl!);
        } catch (e) {
          debugPrint('Error decoding base64 image: $e');
        }
      }

      if (mounted) {
        setState(() {
          _imageBytes = bytes;
          _isLoading = false;
          _hasError = bytes == null;
          if (bytes != null) {
            _autoSelectDisplayMode(bytes);
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading image: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  void _autoSelectDisplayMode(Uint8List bytes) {
    // Auto-select display mode based on image dimensions and device orientation
    // This is a simplified implementation - in practice, you'd decode the image
    // to get actual dimensions and use MediaQuery for orientation

    // For now, default to cover for most cases, but this can be enhanced
    // Could use orientation for smarter defaults in the future
    _displayMode = ImageDisplayMode.cover;
  }

  BoxFit _getBoxFit() {
    switch (_displayMode) {
      case ImageDisplayMode.cover:
        return BoxFit.cover;
      case ImageDisplayMode.contain:
        return BoxFit.contain;
      case ImageDisplayMode.fit:
        return BoxFit.fitWidth;
      case ImageDisplayMode.fill:
        return BoxFit.fill;
    }
  }

  void _handleDoubleTap() {
    Matrix4 endMatrix;
    if (_transformationController.value != Matrix4.identity()) {
      // Reset to identity
      endMatrix = Matrix4.identity();
    } else {
      // Zoom to 2x
      endMatrix = Matrix4.identity()..scale(2.0);
    }

    _animation =
        Matrix4Tween(
          begin: _transformationController.value,
          end: endMatrix,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
          ),
        );

    _animationController.reset();
    _animationController.forward();
  }

  void _cycleDisplayMode() {
    setState(() {
      switch (_displayMode) {
        case ImageDisplayMode.cover:
          _displayMode = ImageDisplayMode.contain;
          break;
        case ImageDisplayMode.contain:
          _displayMode = ImageDisplayMode.fit;
          break;
        case ImageDisplayMode.fit:
          _displayMode = ImageDisplayMode.fill;
          break;
        case ImageDisplayMode.fill:
          _displayMode = ImageDisplayMode.cover;
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ??
          Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black,
            child: const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            ),
          );
    }

    if (_hasError || _imageBytes == null) {
      return widget.errorWidget ??
          Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_not_supported,
                    color: Colors.white54,
                    size: 64,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Image not available',
                    style: TextStyle(color: Colors.white54, fontSize: 16),
                  ),
                ],
              ),
            ),
          );
    }

    return Stack(
      children: [
        // Main image with zoom and pan
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            if (_animation != null) {
              _transformationController.value = _animation!.value;
            }
            return Transform.rotate(
              angle: widget.rotation,
              child: InteractiveViewer(
                transformationController: _transformationController,
                minScale: 0.5,
                maxScale: 4.0,
                onInteractionEnd: (details) {
                  // Reset animation when interaction ends
                  _animation = null;
                },
                child: GestureDetector(
                  onDoubleTap: _handleDoubleTap,
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.black,
                    child: Image.memory(
                      _imageBytes!,
                      fit: _getBoxFit(),
                      gaplessPlayback: true,
                      filterQuality: FilterQuality.high,
                      isAntiAlias: true,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: double.infinity,
                          color: Colors.grey[900],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.broken_image,
                                  color: Colors.white54,
                                  size: 64,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Failed to load image',
                                  style: TextStyle(
                                    color: Colors.white54,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        ),

        // Rotate button (top right)
        if (widget.onRotate != null)
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.rotate_right,
                  color: Colors.white,
                  size: 20,
                ),
                onPressed: widget.onRotate,
                tooltip: 'Rotate Image',
              ),
            ),
          ),

        // Display mode button (top left)
        Positioned(
          top: 16,
          left: 16,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: Icon(_getDisplayModeIcon(), color: Colors.white, size: 20),
              onPressed: _cycleDisplayMode,
              tooltip: 'Display Mode: ${_displayMode.name}',
            ),
          ),
        ),
      ],
    );
  }

  IconData _getDisplayModeIcon() {
    switch (_displayMode) {
      case ImageDisplayMode.cover:
        return Icons.crop_free;
      case ImageDisplayMode.contain:
        return Icons.fit_screen;
      case ImageDisplayMode.fit:
        return Icons.center_focus_strong;
      case ImageDisplayMode.fill:
        return Icons.fullscreen;
    }
  }
}
