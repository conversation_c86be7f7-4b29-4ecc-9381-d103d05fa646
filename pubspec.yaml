name: baby_flashcards
description: "A Flutter app for learning English vocabulary using flashcards with visual learning and audio pronunciation."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.8.1 <4.0.0'
  flutter: '>=3.32.8'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  
  # Firebase
  firebase_core: ^4.0.0
  cloud_firestore: ^6.0.0
  firebase_auth: ^6.0.0
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.2
  path: ^1.9.0
  
  # Image Handling
  image_picker: ^1.0.7
  flutter_image_compress: ^2.1.0
  cached_network_image: ^3.3.1
  
  # Audio
  flutter_tts: ^4.2.3

  # Utilities
  uuid: ^4.3.3
  intl: ^0.20.2
  connectivity_plus: ^6.1.4
  permission_handler: ^12.0.1
  
  # State Management
  provider: ^6.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.8

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/animations/
    - assets/fonts/
