import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';
import '../models/flashcard.dart';
import '../models/category.dart' as model;

class FlashcardService extends ChangeNotifier {
  static const String _boxName = 'flashcards';
  Box<Flashcard>? _box;
  List<Flashcard> _flashcards = [];
  bool _isLoading = false;
  String? _error;
  static const String _categoryBoxName = 'categories';
  Box<model.Category>? _categoryBox;
  List<model.Category> _categories = [];

  // Getters
  List<Flashcard> get flashcards => List.unmodifiable(_flashcards);
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isEmpty => _flashcards.isEmpty;
  int get count => _flashcards.length;
  List<model.Category> get categories => List.unmodifiable(_categories);

  // Initialize the service
  Future<void> initialize() async {
    try {
      _setLoading(true);
      _box = await Hive.openBox<Flashcard>(_boxName);
      _categoryBox = await Hive.openBox<model.Category>(_categoryBoxName);
      await _loadFlashcards();
      await _loadCategories();
      _setError(null);
    } catch (e) {
      _setError('Failed to initialize flashcard service: $e');
      debugPrint('FlashcardService initialization error: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load flashcards from local storage
  Future<void> _loadFlashcards() async {
    if (_box == null) return;

    _flashcards = _box!.values.toList();
    _flashcards.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    notifyListeners();
  }

  // Load categories from local storage
  Future<void> _loadCategories() async {
    if (_categoryBox == null) return;
    _categories = _categoryBox!.values.toList();

    // Fix existing categories that might have enabled: false
    await _fixExistingCategories();

    // Sort categories alphabetically by name
    _categories.sort(
      (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()),
    );
    notifyListeners();
  }

  // Fix existing categories that might have enabled: false
  Future<void> _fixExistingCategories() async {
    bool needsUpdate = false;
    final updatedCategories = <model.Category>[];

    for (final category in _categories) {
      if (!category.enabled) {
        final updatedCategory = category.copyWith(enabled: true);
        updatedCategories.add(updatedCategory);
        needsUpdate = true;
      }
    }

    if (needsUpdate) {
      for (final category in updatedCategories) {
        await _categoryBox?.put(category.id, category);
      }
      _categories = _categoryBox!.values.toList();
    }
  }

  // Add a new flashcard
  Future<Flashcard?> addFlashcard({
    required String word,
    String? localImagePath,
    String? firebaseImageUrl,
    required String category,
    required String familyId,
  }) async {
    try {
      _setError(null);

      final flashcard = Flashcard(
        id: const Uuid().v4(),
        word: word.trim(),
        localImagePath: localImagePath,
        firebaseImageUrl: firebaseImageUrl,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
        category: category,
        familyId: familyId,
      );

      await _box?.put(flashcard.id, flashcard);
      await _loadFlashcards();

      return flashcard;
    } catch (e) {
      _setError('Failed to add flashcard: $e');
      debugPrint('Add flashcard error: $e');
      return null;
    }
  }

  // Update an existing flashcard
  Future<bool> updateFlashcard(Flashcard flashcard) async {
    try {
      _setError(null);

      final updatedFlashcard = flashcard.copyWith(
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      await _box?.put(updatedFlashcard.id, updatedFlashcard);
      await _loadFlashcards();

      return true;
    } catch (e) {
      _setError('Failed to update flashcard: $e');
      debugPrint('Update flashcard error: $e');
      return false;
    }
  }

  // Delete a flashcard
  Future<bool> deleteFlashcard(String id) async {
    try {
      _setError(null);

      await _box?.delete(id);
      await _loadFlashcards();

      return true;
    } catch (e) {
      _setError('Failed to delete flashcard: $e');
      debugPrint('Delete flashcard error: $e');
      return false;
    }
  }

  // Get a flashcard by ID
  Flashcard? getFlashcardById(String id) {
    try {
      return _flashcards.firstWhere((flashcard) => flashcard.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get flashcard by index (for navigation)
  Flashcard? getFlashcardByIndex(int index) {
    if (index < 0 || index >= _flashcards.length) return null;
    return _flashcards[index];
  }

  // Get index of a flashcard
  int getFlashcardIndex(String id) {
    return _flashcards.indexWhere((flashcard) => flashcard.id == id);
  }

  // Clear all flashcards (for sync purposes)
  Future<void> clearAllFlashcards() async {
    try {
      _setError(null);
      await _box?.clear();
      await _loadFlashcards();
    } catch (e) {
      _setError('Failed to clear flashcards: $e');
      debugPrint('Clear flashcards error: $e');
    }
  }

  // Bulk insert flashcards (for sync from Firebase)
  Future<void> bulkInsertFlashcards(List<Flashcard> flashcards) async {
    try {
      _setError(null);

      final Map<String, Flashcard> flashcardMap = {};
      for (final flashcard in flashcards) {
        flashcardMap[flashcard.id] = flashcard;
      }

      await _box?.putAll(flashcardMap);
      await _loadFlashcards();
    } catch (e) {
      _setError('Failed to bulk insert flashcards: $e');
      debugPrint('Bulk insert flashcards error: $e');
    }
  }

  // Get unsynced flashcards
  List<Flashcard> getUnsyncedFlashcards() {
    return _flashcards.where((flashcard) => !flashcard.isSynced).toList();
  }

  // Mark flashcard as synced
  Future<void> markAsSynced(String id) async {
    try {
      final flashcard = getFlashcardById(id);
      if (flashcard != null) {
        final syncedFlashcard = flashcard.copyWith(isSynced: true);
        await _box?.put(id, syncedFlashcard);
        await _loadFlashcards();
      }
    } catch (e) {
      debugPrint('Mark as synced error: $e');
    }
  }

  // Add a new category
  Future<model.Category?> addCategory({
    required String name,
    required String familyId,
  }) async {
    try {
      final category = model.Category(
        id: const Uuid().v4(),
        name: name.trim(),
        familyId: familyId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        enabled: true,
      );
      await _categoryBox?.put(category.id, category);
      await _loadCategories();
      return category;
    } catch (e) {
      _setError('Failed to add category: $e');
      debugPrint('Add category error: $e');
      return null;
    }
  }

  // Update an existing category
  Future<bool> updateCategory(model.Category category) async {
    try {
      final updatedCategory = category.copyWith(updatedAt: DateTime.now());
      await _categoryBox?.put(updatedCategory.id, updatedCategory);
      await _loadCategories();
      return true;
    } catch (e) {
      _setError('Failed to update category: $e');
      debugPrint('Update category error: $e');
      return false;
    }
  }

  // Delete a category
  Future<bool> deleteCategory(String id) async {
    try {
      await _categoryBox?.delete(id);
      await _loadCategories();
      return true;
    } catch (e) {
      _setError('Failed to delete category: $e');
      debugPrint('Delete category error: $e');
      return false;
    }
  }

  // Get a category by ID
  model.Category? getCategoryById(String id) {
    try {
      return _categories.firstWhere((cat) => cat.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get categories by family ID
  List<model.Category> getCategoriesByFamilyId(String familyId) {
    return _categories.where((cat) => cat.familyId == familyId).toList();
  }

  // Get flashcards by category
  List<Flashcard> getFlashcardsByCategory(String category) {
    return _flashcards.where((fc) => fc.category == category).toList();
  }

  // Get flashcards by family ID
  List<Flashcard> getFlashcardsByFamilyId(String familyId) {
    return _flashcards.where((fc) => fc.familyId == familyId).toList();
  }

  // Get flashcards from enabled categories only
  List<Flashcard> getEnabledFlashcards() {
    final enabledCategories = _categories.where((cat) => cat.enabled).toList();
    final enabledCategoryNames = enabledCategories
        .map((cat) => cat.name)
        .toSet();

    return _flashcards
        .where((fc) => enabledCategoryNames.contains(fc.category))
        .toList();
  }

  // Get enabled categories
  List<model.Category> getEnabledCategories() {
    return _categories.where((cat) => cat.enabled).toList();
  }

  // Set category enabled/disabled
  Future<void> setCategoryEnabled(String categoryId, bool enabled) async {
    final idx = _categories.indexWhere((c) => c.id == categoryId);
    if (idx != -1) {
      _categories[idx] = _categories[idx].copyWith(enabled: enabled);
      await _categoryBox?.put(categoryId, _categories[idx]);
      notifyListeners();
    }
  }

  // Add category from sync (without triggering sync again)
  Future<void> addCategoryFromSync(model.Category category) async {
    try {
      await _categoryBox?.put(category.id, category);
      await _loadCategories();
    } catch (e) {
      debugPrint('Add category from sync error: $e');
    }
  }

  // Clear all categories (for sync purposes)
  Future<void> clearAllCategories() async {
    try {
      _setError(null);
      await _categoryBox?.clear();
      await _loadCategories();
    } catch (e) {
      _setError('Failed to clear categories: $e');
      debugPrint('Clear categories error: $e');
    }
  }

  // Bulk insert categories (for sync from Firebase)
  Future<void> bulkInsertCategories(List<model.Category> categories) async {
    try {
      _setError(null);

      final Map<String, model.Category> categoryMap = {};
      for (final category in categories) {
        categoryMap[category.id] = category;
      }

      await _categoryBox?.putAll(categoryMap);
      await _loadCategories();
    } catch (e) {
      _setError('Failed to bulk insert categories: $e');
      debugPrint('Bulk insert categories error: $e');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Dispose resources
  @override
  void dispose() {
    _box?.close();
    _categoryBox?.close();
    super.dispose();
  }
}
